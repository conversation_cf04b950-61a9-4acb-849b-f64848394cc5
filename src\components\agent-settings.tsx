
'use client';

import { useState, useEffect, useTransition } from 'react';
import type { Agent, Tool } from '@/lib/types';
import { cn } from '@/lib/utils';
import { DynamicIcon } from './icons';
import { Label } from './ui/label';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Button } from './ui/button';
import { Checkbox } from './ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import type { ModelProvider, ModelInfo } from '@/lib/models';
import { useToast } from '@/hooks/use-toast';
import { saveAgentSettingsAction } from '@/app/actions';

interface AgentSettingsProps {
  agents: Agent[];
  availableModels: ModelProvider[];
  allTools: Tool[];
}

export function AgentSettings({ agents: initialAgents, availableModels, allTools }: AgentSettingsProps) {
  const { toast } = useToast();
  const [agents, setAgents] = useState<Agent[]>(initialAgents);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(initialAgents[0] || null);
  const [isSaving, startSaving] = useTransition();

  const modelsForProvider: ModelInfo[] = availableModels.find(p => p.id === selectedAgent?.model.provider)?.models || [];
  const isCurrentProviderConfigured: boolean = availableModels.find(p => p.id === selectedAgent?.model.provider)?.configured ?? false;

  useEffect(() => {
    setAgents(initialAgents);
    // Always set the first agent as selected when agents change
    setSelectedAgent(initialAgents[0] || null);
  }, [initialAgents]);

  if (!agents.length) {
    return <div className="p-8 text-center">No agents found.</div>;
  }

  const handleSelectAgent = (agent: Agent) => {
    console.log('🔥 handleSelectAgent called for:', agent.name, agent.id);
    console.log('🔥 Current selectedAgent:', selectedAgent?.name, selectedAgent?.id);

    // Force re-render by creating a new object
    const newSelectedAgent = { ...agent };
    setSelectedAgent(newSelectedAgent);

    console.log('🔥 selectedAgent state updated to:', newSelectedAgent.name, newSelectedAgent.id);

    // Force a small delay to ensure state update
    setTimeout(() => {
      console.log('🔥 After timeout - selectedAgent:', selectedAgent?.name);
    }, 100);
  };

  const handleFormChange = (field: keyof Agent | `model.${keyof Agent['model']}`, value: string) => {
    if (!selectedAgent) return;
  
    const updatedAgent = { ...selectedAgent };
  
    if (field.startsWith('model.')) {
      const modelField = field.split('.')[1] as keyof Agent['model'];
      
      let newProvider = updatedAgent.model.provider;
      let newModelId = updatedAgent.model.modelId;

      if (modelField === 'provider') {
        newProvider = value;
        const provider = availableModels.find(p => p.id === newProvider);
        newModelId = provider?.models[0]?.id || '';
      } else {
        newModelId = value;
      }
      
      updatedAgent.model = { provider: newProvider, modelId: newModelId };
    } else {
      (updatedAgent[field as keyof Agent] as any) = value;
    }
  
    setSelectedAgent(updatedAgent);
  };

  const handleToolToggle = (tool: Tool, checked: boolean | 'indeterminate') => {
    if (!selectedAgent || checked === 'indeterminate') return;

    const currentTools = selectedAgent.tools || [];
    let updatedTools: Tool[];

    if (checked) {
      if (!currentTools.some(t => t.id === tool.id)) {
        updatedTools = [...currentTools, tool];
      } else {
        updatedTools = currentTools;
      }
    } else {
      updatedTools = currentTools.filter(t => t.id !== tool.id);
    }
    
    setSelectedAgent({ ...selectedAgent, tools: updatedTools });
  };

  const handleSaveChanges = (event: React.FormEvent) => {
    event.preventDefault();
    if (!selectedAgent || !isCurrentProviderConfigured) return;

    const updatedAgents = agents.map(agent =>
      agent.id === selectedAgent.id ? selectedAgent : agent
    );
    
    setAgents(updatedAgents);
    
    startSaving(async () => {
        const result = await saveAgentSettingsAction(updatedAgents);
        if (result.success) {
            toast({
              title: 'Settings Saved',
              description: `Settings for all agents have been saved successfully.`,
            });
        } else {
            toast({
              variant: 'destructive',
              title: 'Error Saving Settings',
              description: result.error,
            });
        }
    });
  };

  return (
    <div className="flex flex-1 border-t">
      <aside className="w-64 border-r p-4 bg-card">
        <h2 className="text-lg font-semibold mb-1 px-2">Agents</h2>
        <p className="text-sm text-muted-foreground mb-4 px-2">Manage your agents</p>
        <nav className="flex flex-col gap-1">
          {agents.map((agent) => (
            <button
              key={`${agent.id}-${agent.name}`}
              type="button"
              onClick={(e) => {
                console.log('🔥 Button clicked for:', agent.name, agent.id, 'Event:', e);
                e.preventDefault();
                e.stopPropagation();
                handleSelectAgent(agent);
              }}
              className={cn(
                'flex items-center gap-3 rounded-md p-2 text-left text-sm font-medium transition-colors cursor-pointer',
                selectedAgent?.id === agent.id
                  ? 'bg-secondary text-secondary-foreground'
                  : 'text-muted-foreground hover:bg-muted/50 hover:text-foreground',
                // Debug styling for Cody and Li
                agent.name === 'Cody' && 'border-2 border-red-500',
                agent.name === 'Li' && 'border-2 border-blue-500'
              )}
            >
              <DynamicIcon name={agent.icon} className="h-5 w-5" />
              <span>{agent.name}</span>
            </button>
          ))}
        </nav>
      </aside>

      <main className="flex-1 p-8 overflow-y-auto">
        {selectedAgent ? (
          <form onSubmit={handleSaveChanges}>
            <div className="max-w-2xl mx-auto space-y-8">
              <div>
                <h1 className="text-2xl font-bold tracking-tight">Agent Settings</h1>
                <p className="mt-1 text-muted-foreground">
                  Configure the settings for the <span className="font-semibold text-foreground">{selectedAgent.name}</span> agent.
                </p>
              </div>

              <div>
                <Label htmlFor="agent-name">Agent Name</Label>
                <Input
                  id="agent-name"
                  value={selectedAgent.name}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  className="mt-2"
                />
              </div>

              <div>
                <Label htmlFor="agent-description">Agent Description</Label>
                <Textarea
                  id="agent-description"
                  value={selectedAgent.description}
                  onChange={(e) => handleFormChange('description', e.target.value)}
                  className="mt-2 min-h-[100px]"
                />
              </div>
              
              <div>
                <Label htmlFor="agent-instructions">Agent Instructions</Label>
                <Textarea
                  id="agent-instructions"
                  value={selectedAgent.systemMessage}
                  onChange={(e) => handleFormChange('systemMessage', e.target.value)}
                  className="mt-2 font-code min-h-[200px]"
                />
              </div>

              <div>
                <Label>Language Model</Label>
                <p className="text-sm text-muted-foreground mt-1">Select the provider and model for this agent.</p>
                <div className="grid grid-cols-2 gap-4 mt-2">
                  <Select
                    value={selectedAgent.model.provider}
                    onValueChange={(providerId) => handleFormChange('model.provider', providerId)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select provider...">
                        {availableModels.find(p => p.id === selectedAgent.model.provider)?.name || "Select provider..."}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {availableModels.map(provider => (
                        <SelectItem key={provider.id} value={provider.id}>{provider.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={selectedAgent.model.modelId}
                    onValueChange={(modelId) => handleFormChange('model.modelId', modelId)}
                    disabled={!selectedAgent.model.provider}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select model...">
                        {modelsForProvider.find(m => m.id === selectedAgent.model.modelId)?.name || "Select model..."}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {modelsForProvider.map(model => (
                        <SelectItem key={model.id} value={model.id}>{model.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                {!isCurrentProviderConfigured && selectedAgent.model.provider && (
                    <div className="mt-2 p-3 rounded-md border border-destructive/50 bg-destructive/10">
                        <p className="text-sm text-destructive">
                            The selected provider (<strong>{availableModels.find(p => p.id === selectedAgent.model.provider)?.name}</strong>) is not configured.
                            <br />
                            Please add the <strong>{availableModels.find(p => p.id === selectedAgent.model.provider)?.envKey}</strong> key to your <strong>.env</strong> file.
                        </p>
                    </div>
                )}
              </div>

              <div>
                  <Label>Tools</Label>
                  <p className="text-sm text-muted-foreground mt-1">Enable or disable tools for this agent to use.</p>
                  <div className="space-y-2 mt-4 rounded-md border p-4">
                      {allTools.length > 0 ? allTools.map(tool => (
                          <div key={tool.id} className="flex items-center space-x-3">
                              <Checkbox
                                  id={`tool-checkbox-${tool.id}`}
                                  checked={selectedAgent.tools?.some(t => t.id === tool.id)}
                                  onCheckedChange={(checked) => handleToolToggle(tool, checked)}
                              />
                              <div className="grid gap-1.5 leading-none">
                                  <label htmlFor={`tool-checkbox-${tool.id}`} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                    {tool.name}
                                  </label>
                                  <p className="text-xs text-muted-foreground">
                                    {tool.description}
                                  </p>
                              </div>
                          </div>
                      )) : (
                          <p className="text-sm text-muted-foreground text-center">No tools available. Add one from the Tools page.</p>
                      )}
                  </div>
              </div>
              
              <div className="flex justify-end">
                <Button type="submit" disabled={!isCurrentProviderConfigured || isSaving}>
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </div>
          </form>
        ) : (
          <div className="flex h-full items-center justify-center">
            <p className="text-muted-foreground">Select an agent to view their settings.</p>
          </div>
        )}
      </main>
    </div>
  );
}
